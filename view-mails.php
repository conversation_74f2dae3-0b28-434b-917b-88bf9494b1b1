<?php
// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);


// Include database connection
require 'includes/db.php';

try {
    // Check if mails table exists
    $tableCheck = $pdo->query("SELECT COUNT(*) FROM information_schema.tables 
                              WHERE table_schema = 'mail_system' 
                              AND table_name = 'mails'");

    if ($tableCheck->fetchColumn() == 0) {
        throw new Exception("'mails' table doesn't exist in the database");
    }

    // Fetch all mails
    $stmt = $pdo->query("SELECT * FROM mails ORDER BY submitted_at DESC");
    $mails = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $error = "Database Error: " . $e->getMessage();
} catch (Exception $e) {
    $error = "System Error: " . $e->getMessage();
}
?>
<?php include 'includes/header.php'; ?>

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">📥 All Submitted Mails</h3>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <a href="submit-mail.php" class="btn btn-success">+ Submit New Mail</a>
                        <a href="leader-dashboard.php" class="btn btn-outline-primary">📊 Dashboard</a>
                    </div>

                    <?php if (isset($error)): ?>
                        <div class="alert alert-danger" role="alert">
                            <h4 class="alert-heading">🚨 Error Loading Mails</h4>
                            <p><?= htmlspecialchars($error) ?></p>
                            <hr>
                            <p class="mb-0">Please check your database configuration and ensure the 'mails' table exists.</p>
                        </div>

                    <?php elseif (empty($mails)): ?>
                        <div class="alert alert-info" role="alert">
                            <h4 class="alert-heading">ℹ️ No Mails Found</h4>
                            <p class="mb-0">No mails have been submitted yet. The system is ready to receive mails.</p>
                        </div>

                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>ID</th>
                                        <th>Sender</th>
                                        <th>Subject</th>
                                        <th>Summary</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($mails as $row): ?>
                                        <tr>
                                            <td><span class="badge bg-secondary">#<?= $row['id'] ?></span></td>
                                            <td><?= htmlspecialchars($row['sender_name'] ?? 'N/A') ?></td>
                                            <td><?= htmlspecialchars($row['subject'] ?? 'No subject') ?></td>
                                            <td><?= htmlspecialchars(mb_strimwidth($row['summary'] ?? '—', 0, 50, '...')) ?></td>
                                            <td>
                                                <?php
                                                $status = $row['status'] ?? 'pending';
                                                $badgeClass = match($status) {
                                                    'pending' => 'bg-warning',
                                                    'approved' => 'bg-success',
                                                    'rejected' => 'bg-danger',
                                                    default => 'bg-secondary'
                                                };
                                                ?>
                                                <span class="badge <?= $badgeClass ?>"><?= ucfirst($status) ?></span>
                                            </td>
                                            <td><?= date('M j, Y', strtotime($row['submitted_at'])) ?></td>
                                            <td>
                                                <a class="btn btn-sm btn-outline-primary" href="view-mail-details.php?id=<?= $row['id'] ?>">
                                                    👁️ View
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>