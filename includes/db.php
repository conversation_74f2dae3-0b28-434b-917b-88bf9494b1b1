<?php
$host = 'localhost';      // If using Docker, use 'db' if defined in docker-compose
$db   = 'mail_system';    // Name of the database
$user = 'root';           // Default MySQL user
$pass = '';               // Replace with your root password if set
$charset = 'utf8mb4';

$dsn = "mysql:host=$host;dbname=$db;charset=$charset";

$options = [
    PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION, // Enable error reporting
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,       // Return associative arrays
    PDO::ATTR_EMULATE_PREPARES   => false,                  // Native prepared statements
];

try {
    $pdo = new PDO($dsn, $user, $pass, $options);
} catch (\PDOException $e) {
    die('Database Connection Failed: ' . $e->getMessage());
}
?>
