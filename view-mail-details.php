<?php
// Enable error reporting
ini_set('display_errors', 1);
error_reporting(E_ALL);

require 'includes/db.php';

// Validate ID
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    die("<div class='alert alert-danger'>No mail ID provided.</div>");
}

$mail_id = (int) $_GET['id'];

try {
    $stmt = $pdo->prepare("SELECT * FROM mails WHERE id = ?");
    $stmt->execute([$mail_id]);
    $mail = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$mail) {
        die("<div class='alert alert-danger'>Mail not found.</div>");
    }
} catch (PDOException $e) {
    die("Database error: " . $e->getMessage());
}
?>

<?php include 'includes/header.php'; ?>

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">📄 Mail Details</h3>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <a href="view-mails.php" class="btn btn-outline-secondary">← Back to All Mails</a>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Sender Name:</label>
                                <div class="p-2 bg-light rounded"><?= htmlspecialchars($mail['sender_name']) ?></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Submitted At:</label>
                                <div class="p-2 bg-light rounded"><?= date('M j, Y g:i A', strtotime($mail['submitted_at'])) ?></div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold">Subject:</label>
                        <div class="p-2 bg-light rounded"><?= htmlspecialchars($mail['subject']) ?></div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold">Message:</label>
                        <div class="p-3 bg-light rounded" style="min-height: 100px;">
                            <?= nl2br(htmlspecialchars($mail['message'])) ?>
                        </div>
                    </div>

                    <?php if (!empty($mail['attachment'])): ?>
                        <div class="mb-3">
                            <label class="form-label fw-bold">Attachment:</label>
                            <div class="p-2 bg-light rounded">
                                <a href="<?= htmlspecialchars($mail['attachment']) ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                                    📎 View Attachment
                                </a>
                            </div>
                        </div>
                    <?php endif; ?>

                    <hr>

                    <!-- Summary form -->
                    <div class="card">
                        <div class="card-header bg-secondary text-white">
                            <h5 class="mb-0">📝 Create Summary</h5>
                        </div>
                        <div class="card-body">
                            <form action="process-summary.php" method="POST">
                                <input type="hidden" name="mail_id" value="<?= $mail_id ?>">

                                <div class="mb-3">
                                    <label for="summary" class="form-label">Summary:</label>
                                    <textarea class="form-control" id="summary" name="summary" rows="4" required
                                              placeholder="Write a concise summary of this mail..."></textarea>
                                </div>

                                <div class="mb-3">
                                    <label for="purpose" class="form-label">Send to Leader as:</label>
                                    <select class="form-select" id="purpose" name="purpose" required>
                                        <option value="">-- Select Action --</option>
                                        <option value="Action">📋 For Action</option>
                                        <option value="Information">ℹ️ For Information</option>
                                        <option value="Record">📁 For Record</option>
                                    </select>
                                </div>

                                <div class="d-grid">
                                    <button type="submit" class="btn btn-success">📤 Send Summary to Leader</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
