<?php
require 'includes/db.php';


ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);




if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['actions'])) {
    $actions = $_POST['actions'];
    $now = date('Y-m-d H:i:s');
    $updatedCount = 0;

    foreach ($actions as $mailId => $action) {
        if (!in_array($action, ['Approved', 'Rejected', 'Follow Up'])) {
            continue; // Invalid action
        }
       

        // Step 1: Update the mails table
        $stmt = $pdo->prepare("UPDATE mails SET leader_action = ?, status = 'acted', action_date = ? WHERE id = ?");
        $stmt->execute([$action, $now, $mailId]);

        // Step 2: Log into mail_tracking
        $track = $pdo->prepare("INSERT INTO mail_tracking (mail_id, action_taken, remarks, action_by, action_date)
                                VALUES (?, ?, ?, ?, ?)");
        $remarks = "Leader decided to '$action'";
        $track->execute([$mailId, $action, $remarks, 'Leader', $now]);

        // Step 3 (Optional): Route back to department on Follow Up
        if ($action === 'Follow Up') {
            $route = $pdo->prepare("UPDATE mails SET status = 'routed_back' WHERE id = ?");
            $route->execute([$mailId]);
        }

        $updatedCount++;
    }

    echo "<p style='font-family: Arial; padding: 20px; background: #d4edda; color: #155724; border: 1px solid #c3e6cb; border-radius: 5px;'>
            ✅ $updatedCount mail(s) updated and tracked successfully. <a href='leader-dashboard.php'>Return to Dashboard</a>
          </p>";

} else {
    echo "<p style='font-family: Arial; padding: 20px; background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; border-radius: 5px;'>
            ❌ No actions submitted. <a href='leader-dashboard.php'>Try again</a>.
          </p>";
}

?>
