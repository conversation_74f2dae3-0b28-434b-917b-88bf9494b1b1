<?php
require_once 'includes/db.php';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $sender_name  = $_POST['sender_name'];
    $sender_email = $_POST['sender_email'];
    $subject      = $_POST['subject'];
    $message      = $_POST['message'];
    $attachment   = '';
    $maxFileSize  = 2 * 1024 * 1024; // 2MB
    $allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'image/jpeg', 'image/png'];

    // Handle file upload with validation
    if (isset($_FILES['attachment']) && $_FILES['attachment']['error'] === 0) {
        $fileType = $_FILES['attachment']['type'];
        $fileSize = $_FILES['attachment']['size'];

        if (!in_array($fileType, $allowedTypes)) {
            die("<div class='alert alert-danger'>❌ Invalid file type. Only PDF, DOCX, JPG, PNG allowed.</div>");
        }

        if ($fileSize > $maxFileSize) {
            die("<div class='alert alert-danger'>❌ File too large. Maximum allowed size is 2MB.</div>");
        }

        $upload_dir = "assets/uploads/";
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }

        $original_name = basename($_FILES['attachment']['name']);
        $new_filename  = time() . "_" . $original_name;
        $target_path   = $upload_dir . $new_filename;

        if (move_uploaded_file($_FILES["attachment"]["tmp_name"], $target_path)) {
            $attachment = $target_path;
        } else {
            die("<div class='alert alert-danger'>❌ File upload failed.</div>");
        }
    }

    // Save to database
    $stmt = $pdo->prepare("INSERT INTO mails (sender_name, sender_email, subject, message, attachment, status) VALUES (?, ?, ?, ?, ?, 'pending')");
    $stmt->execute([$sender_name, $sender_email, $subject, $message, $attachment]);
    

    // Get last inserted mail ID
    $mail_id = $pdo->lastInsertId();

    // Redirect to success page with reference number
    header("Location: success.php?ref=" . $mail_id);
    exit;
}
?>

<?php include 'includes/header.php'; ?>

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow">
                <div class="card-header bg-primary text-white text-center">
                    <h3 class="mb-0">📬 Submit a Mail to the Organization</h3>
                </div>
                <div class="card-body">
                    <form action="submit-mail.php" method="post" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="sender_name" class="form-label">Your Name:</label>
                            <input type="text" class="form-control" id="sender_name" name="sender_name" required>
                        </div>

                        <div class="mb-3">
                            <label for="sender_email" class="form-label">Your Email (optional):</label>
                            <input type="email" class="form-control" id="sender_email" name="sender_email">
                        </div>

                        <div class="mb-3">
                            <label for="subject" class="form-label">Subject:</label>
                            <input type="text" class="form-control" id="subject" name="subject" required>
                        </div>

                        <div class="mb-3">
                            <label for="message" class="form-label">Message:</label>
                            <textarea class="form-control" id="message" name="message" rows="6" required></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="attachment" class="form-label">Attachment (optional):</label>
                            <input type="file" class="form-control" id="attachment" name="attachment">
                            <div class="form-text">Allowed: PDF, DOCX, JPG, PNG (Max: 2MB)</div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">📤 Submit Mail</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>