<?php
require 'includes/db.php';

// Enable error reporting
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Validate input
$mailId = $_POST['mail_id'] ?? null;
$summary = trim($_POST['summary'] ?? '');
$purpose = $_POST['purpose'] ?? '';

if (!$mailId || !$summary || !$purpose) {
    die("Missing required fields.");
}

try {
    // ✅ Update summary, purpose, and mark mail as 'summarized'
    $stmt = $pdo->prepare("UPDATE mails SET summary = ?, purpose = ?, status = 'summarized' WHERE id = ?");
    $stmt->execute([$summary, $purpose, $mailId]);

        // ✅ Step 2: Set status = 'summarized'
        $stmt = $pdo->prepare("UPDATE mails SET status = 'summarized' WHERE id = ?");
        $stmt->execute([$mailId]);


    echo "<h2>✅ Summary submitted and mail marked as 'summarized'!</h2>";
    echo '<a href="view-mails.php">← Back to Mails</a>';
} catch (PDOException $e) {
    die("Database Error: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Summary Submitted</title>
</head>
<body>
</body>
</html>
