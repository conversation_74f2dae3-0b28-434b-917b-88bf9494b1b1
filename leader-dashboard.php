<?php
require 'includes/db.php';

// Fetch summarized mails
$stmt = $pdo->prepare("SELECT id, sender_name, subject, summary, purpose FROM mails WHERE status = 'summarized'");
$stmt->execute();
$mails = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<?php include 'includes/header.php'; ?>

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">📬 Leader Dashboard – Review Summarized Mails</h3>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <a href="view-mails.php" class="btn btn-outline-secondary">📋 View All Mails</a>
                        <a href="submit-mail.php" class="btn btn-outline-success">+ Submit New Mail</a>
                    </div>

                    <?php if (count($mails) === 0): ?>
                        <div class="alert alert-info" role="alert">
                            <h4 class="alert-heading">ℹ️ No Summarized Mails</h4>
                            <p class="mb-0">No summarized mails are available for review at this time.</p>
                        </div>
                    <?php else: ?>
                        <form action="process-leader-actions.php" method="POST">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Sender</th>
                                            <th>Subject</th>
                                            <th>Summary</th>
                                            <th>Purpose</th>
                                            <th>Leader Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($mails as $mail): ?>
                                            <tr>
                                                <td><?= htmlspecialchars($mail['sender_name']) ?></td>
                                                <td><?= htmlspecialchars($mail['subject']) ?></td>
                                                <td><?= nl2br(htmlspecialchars($mail['summary'])) ?></td>
                                                <td><?= htmlspecialchars($mail['purpose']) ?></td>
                                                <td>
                                                    <select name="actions[<?= $mail['id'] ?>]" class="form-select" required>
                                                        <option value="">-- Select Action --</option>
                                                        <option value="Approved">✅ Approve</option>
                                                        <option value="Rejected">❌ Reject</option>
                                                        <option value="Follow Up">🔄 Follow Up</option>
                                                    </select>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <button type="submit" class="btn btn-success btn-lg">📤 Submit All Decisions</button>
                            </div>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
