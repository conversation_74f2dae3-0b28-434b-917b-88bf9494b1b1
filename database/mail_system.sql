-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: localhost
-- Generation Time: Aug 03, 2025 at 09:51 AM
-- Server version: 10.4.28-MariaDB
-- PHP Version: 8.0.28

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `mail_system`
--

-- --------------------------------------------------------

--
-- Table structure for table `actions`
--

CREATE TABLE `actions` (
  `id` int(11) NOT NULL,
  `summary_id` int(11) NOT NULL,
  `leader_id` int(11) NOT NULL,
  `decision` enum('take_action','info_action','inform_only','record') NOT NULL,
  `notes` text DEFAULT NULL,
  `decided_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `audit_log`
--

CREATE TABLE `audit_log` (
  `id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `action` varchar(255) DEFAULT NULL,
  `reference_id` int(11) DEFAULT NULL,
  `action_time` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `departments`
--

CREATE TABLE `departments` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `mails`
--

CREATE TABLE `mails` (
  `id` int(11) NOT NULL,
  `sender_name` varchar(100) DEFAULT NULL,
  `sender_email` varchar(100) DEFAULT NULL,
  `subject` varchar(255) DEFAULT NULL,
  `message` text DEFAULT NULL,
  `attachment` varchar(255) DEFAULT NULL,
  `status` enum('pending','assigned','summarized','acted') DEFAULT 'pending',
  `department_id` int(11) DEFAULT NULL,
  `received_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `submitted_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `summary` text DEFAULT NULL,
  `forwarded_to_leader` tinyint(1) DEFAULT 0,
  `purpose` enum('Action','Information','information & action','record') DEFAULT NULL,
  `leader_action` varchar(50) DEFAULT NULL,
  `action_date` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `mails`
--

INSERT INTO `mails` (`id`, `sender_name`, `sender_email`, `subject`, `message`, `attachment`, `status`, `department_id`, `received_at`, `submitted_at`, `summary`, `forwarded_to_leader`, `purpose`, `leader_action`, `action_date`) VALUES
(1, 'kefa', '<EMAIL>', 'provision of new years', 'fkdfyugfuaso fgfd', '', 'acted', NULL, '2025-07-24 07:47:36', '2025-07-24 10:31:26', 'Sample summary', 0, 'Action', 'Approved', '2025-07-31 14:15:44'),
(2, 'kefa1', '<EMAIL>', 'provision of new  IDE', 'it excellence to have a new idea for the great the form ahda ahskaksf sg', '', 'acted', NULL, '2025-07-24 08:01:34', '2025-07-24 10:31:26', 'vscode', 0, 'Action', 'Approved', '2025-08-01 07:35:13'),
(3, 'dote', '<EMAIL>', 'internet connectivity', 'the change of the ip address', '', 'acted', NULL, '2025-07-24 08:58:24', '2025-07-24 10:31:26', 'troubleshooting', 0, 'Action', 'Approved', '2025-08-01 07:35:13'),
(4, 'eric', '<EMAIL>', 'optical fibre rack', 'the is the huge damn and it is fixed', '', 'pending', NULL, '2025-07-25 05:34:09', '2025-07-25 05:34:09', 'work on it', 0, 'record', NULL, NULL),
(5, 'kefa1', '<EMAIL>', 'optical fibre rack', 'good for testing', '', 'acted', NULL, '2025-07-31 05:17:14', '2025-07-31 05:17:14', 'biaigdfcgdhfb', 0, 'Information', 'Approved', '2025-08-01 11:30:21'),
(6, 'deo', '<EMAIL>', 'provision of new years', 'ga fcvyvhsiev', '', 'acted', NULL, '2025-08-01 09:25:27', '2025-08-01 09:25:27', 'bf a8 ystf9bpsfyw7', 0, 'Action', 'Follow Up', '2025-08-01 21:59:47');

-- --------------------------------------------------------

--
-- Table structure for table `mail_tracking`
--

CREATE TABLE `mail_tracking` (
  `id` int(11) NOT NULL,
  `mail_id` int(11) NOT NULL,
  `action_taken` varchar(50) DEFAULT NULL,
  `remarks` text DEFAULT NULL,
  `action_by` varchar(50) DEFAULT NULL,
  `action_date` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `mail_tracking`
--

INSERT INTO `mail_tracking` (`id`, `mail_id`, `action_taken`, `remarks`, `action_by`, `action_date`) VALUES
(1, 1, 'Approved', 'Leader decided to \'Approved\'', 'Leader', '2025-07-31 14:15:44'),
(2, 5, 'Approved', 'Leader decided to \'Approved\'', 'Leader', '2025-07-31 14:15:44'),
(3, 5, 'Approved', 'Leader decided to \'Approved\'', 'Leader', '2025-08-01 06:42:24'),
(4, 2, 'Approved', 'Leader decided to \'Approved\'', 'Leader', '2025-08-01 07:35:13'),
(5, 3, 'Approved', 'Leader decided to \'Approved\'', 'Leader', '2025-08-01 07:35:13'),
(6, 5, 'Approved', 'Leader decided to \'Approved\'', 'Leader', '2025-08-01 11:30:21'),
(7, 6, 'Follow Up', 'Leader decided to \'Follow Up\'', 'Leader', '2025-08-01 21:59:47');

-- --------------------------------------------------------

--
-- Table structure for table `summaries`
--

CREATE TABLE `summaries` (
  `id` int(11) NOT NULL,
  `mail_id` int(11) NOT NULL,
  `summary_text` text DEFAULT NULL,
  `urgency` enum('low','medium','high') DEFAULT 'medium',
  `suggested_action` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` enum('admin','department','leader') NOT NULL,
  `department_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `actions`
--
ALTER TABLE `actions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `summary_id` (`summary_id`),
  ADD KEY `leader_id` (`leader_id`);

--
-- Indexes for table `audit_log`
--
ALTER TABLE `audit_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `departments`
--
ALTER TABLE `departments`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `mails`
--
ALTER TABLE `mails`
  ADD PRIMARY KEY (`id`),
  ADD KEY `department_id` (`department_id`);

--
-- Indexes for table `mail_tracking`
--
ALTER TABLE `mail_tracking`
  ADD PRIMARY KEY (`id`),
  ADD KEY `mail_id` (`mail_id`);

--
-- Indexes for table `summaries`
--
ALTER TABLE `summaries`
  ADD PRIMARY KEY (`id`),
  ADD KEY `mail_id` (`mail_id`),
  ADD KEY `created_by` (`created_by`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `department_id` (`department_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `actions`
--
ALTER TABLE `actions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `audit_log`
--
ALTER TABLE `audit_log`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `departments`
--
ALTER TABLE `departments`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `mails`
--
ALTER TABLE `mails`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `mail_tracking`
--
ALTER TABLE `mail_tracking`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `summaries`
--
ALTER TABLE `summaries`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `actions`
--
ALTER TABLE `actions`
  ADD CONSTRAINT `actions_ibfk_1` FOREIGN KEY (`summary_id`) REFERENCES `summaries` (`id`),
  ADD CONSTRAINT `actions_ibfk_2` FOREIGN KEY (`leader_id`) REFERENCES `users` (`id`);

--
-- Constraints for table `audit_log`
--
ALTER TABLE `audit_log`
  ADD CONSTRAINT `audit_log_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);

--
-- Constraints for table `mails`
--
ALTER TABLE `mails`
  ADD CONSTRAINT `mails_ibfk_1` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `mail_tracking`
--
ALTER TABLE `mail_tracking`
  ADD CONSTRAINT `mail_tracking_ibfk_1` FOREIGN KEY (`mail_id`) REFERENCES `mails` (`id`);

--
-- Constraints for table `summaries`
--
ALTER TABLE `summaries`
  ADD CONSTRAINT `summaries_ibfk_1` FOREIGN KEY (`mail_id`) REFERENCES `mails` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `summaries_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `users`
--
ALTER TABLE `users`
  ADD CONSTRAINT `users_ibfk_1` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`) ON DELETE SET NULL;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
