/* Custom styles to complement Bootstrap */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #0b5ed7, #6a0dad);
    min-height: 100vh;
    padding-top: 0;
}

/* Custom card animations */
.card {
    animation: slide-in 0.5s ease-in-out;
    border: none;
}

.card-header {
    border-bottom: none;
}

/* Custom focus styles for form elements */
.form-control:focus,
.form-select:focus {
    border-color: #6a0dad;
    box-shadow: 0 0 0 0.2rem rgba(106, 13, 173, 0.25);
}

/* Custom button hover effects */
.btn-primary:hover {
    background-color: #6a0dad;
    border-color: #6a0dad;
}

.btn-success:hover {
    background-color: #198754;
    border-color: #198754;
}

/* Custom navbar styling */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

/* Custom table styling */
.table-hover tbody tr:hover {
    background-color: rgba(106, 13, 173, 0.1);
}

/* Custom alert styling */
.alert {
    border: none;
    border-radius: 10px;
}

/* Animation for cards */
@keyframes slide-in {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Custom spacing for main content */
.container {
    margin-bottom: 2rem;
}

/* Custom badge styling */
.badge {
    font-size: 0.8em;
}

/* Hero Section Styles */
.hero-section {
    background: linear-gradient(135deg, #0b5ed7, #6a0dad);
    min-height: 100vh;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.min-vh-75 {
    min-height: 75vh;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: 1.5rem;
}

.text-gradient {
    background: linear-gradient(45deg, #fff, #e3f2fd);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    line-height: 1.6;
    max-width: 500px;
}

.hero-buttons .btn {
    padding: 12px 30px;
    font-weight: 600;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.hero-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

/* Floating Card Animation */
.floating-card {
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.stats-row {
    display: flex;
    justify-content: space-around;
    margin-top: 1rem;
}

.stat-item {
    text-align: center;
}

/* Features Section */
.features-section {
    background: #f8f9fa;
    position: relative;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto;
}

.feature-card {
    background: white;
    border-radius: 20px;
    padding: 2.5rem 2rem;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.5s;
}

.feature-card:hover::before {
    left: 100%;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

.feature-icon {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin: 0 auto 1.5rem;
    color: white;
}

.feature-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #333;
}

.feature-description {
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.feature-list {
    margin-bottom: 2rem;
}

.feature-item {
    color: #28a745;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.btn-feature {
    border-radius: 50px;
    padding: 12px 25px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-feature:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.15);
}

/* Stats Section */
.stats-section {
    background: white;
}

.stat-card {
    padding: 1.5rem;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #666;
    font-weight: 500;
    text-transform: uppercase;
    font-size: 0.9rem;
    letter-spacing: 1px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }

    .card {
        margin: 0.5rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .feature-card {
        padding: 2rem 1.5rem;
    }

    .hero-buttons .btn {
        display: block;
        width: 100%;
        margin-bottom: 1rem;
    }
}
